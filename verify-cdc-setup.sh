#!/bin/bash

# CDC Setup Verification Script
# This script verifies that the CDC setup is properly configured

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_success() {
    echo -e "${GREEN}✓${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

# Verification functions
verify_files() {
    print_header "Verifying Configuration Files"
    
    local files=("source-connector.json" "sink-connector.json" "deploy-cdc.sh" "manage-cdc.sh")
    local all_good=true
    
    for file in "${files[@]}"; do
        if [[ -f "$file" ]]; then
            print_success "Found: $file"
            
            # Validate JSON files
            if [[ "$file" == *.json ]]; then
                if jq empty "$file" 2>/dev/null; then
                    print_success "Valid JSON: $file"
                else
                    print_error "Invalid JSON: $file"
                    all_good=false
                fi
            fi
            
            # Check if scripts are executable
            if [[ "$file" == *.sh ]]; then
                if [[ -x "$file" ]]; then
                    print_success "Executable: $file"
                else
                    print_warning "Not executable: $file (run: chmod +x $file)"
                fi
            fi
        else
            print_error "Missing: $file"
            all_good=false
        fi
    done
    
    return $all_good
}

verify_docker_services() {
    print_header "Verifying Docker Services"
    
    local services=("postgres" "broker" "schema-registry" "connect")
    local all_good=true
    
    for service in "${services[@]}"; do
        if docker ps --format "table {{.Names}}" | grep -q "^$service$"; then
            print_success "Running: $service"
        else
            print_error "Not running: $service"
            all_good=false
        fi
    done
    
    return $all_good
}

verify_connectivity() {
    print_header "Verifying Service Connectivity"
    
    local all_good=true
    
    # Check PostgreSQL
    if docker exec postgres pg_isready -U postgres > /dev/null 2>&1; then
        print_success "PostgreSQL is ready"
    else
        print_error "PostgreSQL is not ready"
        all_good=false
    fi
    
    # Check Kafka Connect
    if curl -s -f http://localhost:8083 > /dev/null 2>&1; then
        print_success "Kafka Connect is ready"
    else
        print_error "Kafka Connect is not ready"
        all_good=false
    fi
    
    # Check Schema Registry
    if curl -s -f http://localhost:8081 > /dev/null 2>&1; then
        print_success "Schema Registry is ready"
    else
        print_error "Schema Registry is not ready"
        all_good=false
    fi
    
    return $all_good
}

verify_postgres_setup() {
    print_header "Verifying PostgreSQL Setup"
    
    local all_good=true
    
    # Check if schemas exist
    schemas=$(docker exec postgres psql -U postgres -d postgres -t -c "SELECT schema_name FROM information_schema.schemata WHERE schema_name IN ('fahasa', 'pim');")
    
    if echo "$schemas" | grep -q "fahasa"; then
        print_success "Schema 'fahasa' exists"
    else
        print_warning "Schema 'fahasa' does not exist (will be created during deployment)"
    fi
    
    if echo "$schemas" | grep -q "pim"; then
        print_success "Schema 'pim' exists"
    else
        print_warning "Schema 'pim' does not exist (will be created during deployment)"
    fi
    
    # Check WAL level
    wal_level=$(docker exec postgres psql -U postgres -d postgres -t -c "SHOW wal_level;" | xargs)
    if [[ "$wal_level" == "logical" ]]; then
        print_success "WAL level is set to 'logical' (required for CDC)"
    else
        print_error "WAL level is '$wal_level', should be 'logical'"
        all_good=false
    fi
    
    return $all_good
}

verify_kafka_connect_plugins() {
    print_header "Verifying Kafka Connect Plugins"
    
    local all_good=true
    
    # Check if required connectors are available
    plugins=$(curl -s http://localhost:8083/connector-plugins | jq -r '.[].class')
    
    if echo "$plugins" | grep -q "io.debezium.connector.postgresql.PostgresConnector"; then
        print_success "Debezium PostgreSQL connector is available"
    else
        print_error "Debezium PostgreSQL connector is not available"
        all_good=false
    fi
    
    if echo "$plugins" | grep -q "io.confluent.connect.jdbc.JdbcSinkConnector"; then
        print_success "JDBC Sink connector is available"
    else
        print_error "JDBC Sink connector is not available"
        all_good=false
    fi
    
    return $all_good
}

verify_connector_configs() {
    print_header "Verifying Connector Configurations"
    
    local all_good=true
    
    # Verify source connector config
    print_info "Checking source connector configuration..."
    source_name=$(jq -r '.name' source-connector.json)
    source_class=$(jq -r '.config."connector.class"' source-connector.json)
    source_table=$(jq -r '.config."table.include.list"' source-connector.json)
    
    if [[ "$source_name" == "fahasa-m-product-source-connector" ]]; then
        print_success "Source connector name is correct"
    else
        print_error "Source connector name is incorrect: $source_name"
        all_good=false
    fi
    
    if [[ "$source_class" == "io.debezium.connector.postgresql.PostgresConnector" ]]; then
        print_success "Source connector class is correct"
    else
        print_error "Source connector class is incorrect: $source_class"
        all_good=false
    fi
    
    if [[ "$source_table" == "fahasa.m_product" ]]; then
        print_success "Source table configuration is correct"
    else
        print_error "Source table configuration is incorrect: $source_table"
        all_good=false
    fi
    
    # Verify sink connector config
    print_info "Checking sink connector configuration..."
    sink_name=$(jq -r '.name' sink-connector.json)
    sink_class=$(jq -r '.config."connector.class"' sink-connector.json)
    sink_topic=$(jq -r '.config.topics' sink-connector.json)
    sink_table=$(jq -r '.config."table.name.format"' sink-connector.json)
    
    if [[ "$sink_name" == "pim-m-product-sink-connector" ]]; then
        print_success "Sink connector name is correct"
    else
        print_error "Sink connector name is incorrect: $sink_name"
        all_good=false
    fi
    
    if [[ "$sink_class" == "io.confluent.connect.jdbc.JdbcSinkConnector" ]]; then
        print_success "Sink connector class is correct"
    else
        print_error "Sink connector class is incorrect: $sink_class"
        all_good=false
    fi
    
    if [[ "$sink_topic" == "debezium.fahasa.m_product" ]]; then
        print_success "Sink topic configuration is correct"
    else
        print_error "Sink topic configuration is incorrect: $sink_topic"
        all_good=false
    fi
    
    if [[ "$sink_table" == "pim.m_product" ]]; then
        print_success "Sink table configuration is correct"
    else
        print_error "Sink table configuration is incorrect: $sink_table"
        all_good=false
    fi
    
    return $all_good
}

show_next_steps() {
    print_header "Next Steps"
    
    echo "1. Deploy the CDC pipeline:"
    echo "   ./deploy-cdc.sh"
    echo ""
    echo "2. Check the status:"
    echo "   ./manage-cdc.sh status"
    echo ""
    echo "3. Test with sample data:"
    echo "   ./manage-cdc.sh test"
    echo ""
    echo "4. Monitor the pipeline:"
    echo "   ./manage-cdc.sh monitor"
    echo ""
    echo "5. Access Confluent Control Center:"
    echo "   http://localhost:9021"
}

# Main verification function
main() {
    print_header "CDC Setup Verification"
    print_info "Verifying CDC configuration for fahasa.m_product → pim.m_product"
    
    local overall_status=true
    
    # Run all verifications
    verify_files || overall_status=false
    verify_docker_services || overall_status=false
    verify_connectivity || overall_status=false
    verify_postgres_setup || overall_status=false
    verify_kafka_connect_plugins || overall_status=false
    verify_connector_configs || overall_status=false
    
    # Show results
    print_header "Verification Results"
    
    if $overall_status; then
        print_success "All verifications passed! CDC setup is ready for deployment."
        show_next_steps
    else
        print_error "Some verifications failed. Please fix the issues before deploying."
        echo ""
        print_info "Common fixes:"
        echo "- Start missing Docker services"
        echo "- Wait for services to be fully ready"
        echo "- Check Docker Compose configurations"
        echo "- Verify network connectivity"
    fi
    
    return $overall_status
}

# Run main function
main "$@"
