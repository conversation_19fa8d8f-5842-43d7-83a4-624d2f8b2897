# Confluent Platform

Confluent Platform is a streaming platform that enables you to organize and manage data from various sources as real-time streams.

## Getting Started

This section will guide you through setting up and running the Confluent Platform using Docker Compose. The provided `Makefile` simplifies the process.

## Prerequisites

Before you begin, ensure you have the following installed:
- Docker
- Docker Compose

## Installation

1. **Clone the repository:**
   ```bash
   git clone <your-repository-url>
   cd confluent-platform
   ```

2. **Start all services (Confluent Platform and PostgreSQL):**
   ```bash
   make up
   ```
   Alternatively, you can start services individually:
   - Confluent Platform: `make up-confluent`
   - PostgreSQL: `make up-postgres`

## Usage

### Managing Services

The `Makefile` provides convenient targets to manage your services:

- **Start all services:** `make up`
- **Stop all services:** `make down`
- **View logs for all services:** `make logs`
- **List running services:** `make ps`

- **Start Confluent Platform only:** `make up-confluent`
- **Stop Confluent Platform only:** `make down-confluent`
- **View Confluent Platform logs:** `make logs-confluent`
- **List Confluent Platform services:** `make ps-confluent`

- **Start PostgreSQL only:** `make up-postgres`
- **Stop PostgreSQL only:** `make down-postgres`
- **View PostgreSQL logs:** `make logs-postgres`
- **List PostgreSQL services:** `make ps-postgres`

### Cleaning Up

To stop all services and remove containers and networks:
```bash
make clean
```

### Accessing Confluent Control Center

Once the services are running, you can typically access Confluent Control Center by navigating to `http://localhost:9021` in your web browser.

## Contributing

Guidelines for contributing to this project.

## License

This project is licensed under the [Specify License Here] License.