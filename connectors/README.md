# 🔌 Kafka Connectors

A comprehensive guide to managing and configuring Kafka Connect connectors in this project.

## 🎯 Purpose and Overview

This directory provides a standardized approach for:
- Organizing source and sink connectors by type and technology
- Managing connector configurations in a version-controlled manner
- Simplifying connector deployment and maintenance
- Ensuring consistent configuration across environments

## 📁 Directory Structure

```
connectors/
├── source/              # Source connectors
│   ├── jdbc/           # JDBC source connectors
│   │   ├── postgresql/ # PostgreSQL configurations
│   │   └── mysql/      # MySQL configurations
│   └── other_sources/  # Additional source types
├── sink/               # Sink connectors
│   ├── jdbc/          # JDBC sink connectors
│   │   ├── postgresql/ # PostgreSQL configurations
│   │   └── mysql/      # MySQL configurations
│   └── other_sinks/   # Additional sink types
└── lib/               # Connector libraries and dependencies
```

## 🚀 Usage Guide

### Managing Connectors

Use the Makefile commands from the project root:

```bash
# List available connectors
make list-connectors

# Deploy all connectors
make create-connectors

# Deploy source connectors only
make create-source-connectors

# Deploy sink connectors only
make create-sink-connectors

# Deploy specific connector
make create-source-connector CONNECTOR=jdbc-postgresql

# Remove connector
make delete-sink-connector CONNECTOR=jdbc-postgresql

# Monitor status
make check-connector-status
```

## ⚙️ Configuration Guide

### Connector Configuration Files

Each connector is defined in a JSON file with this structure:

```json
{
  "name": "connector-name",
  "config": {
    "connector.class": "io.confluent.connect.jdbc.JdbcSourceConnector",
    "tasks.max": "1",
    "connection.url": "**************************************",
    "connection.user": "${env:DB_USER}",
    "connection.password": "${env:DB_PASSWORD}",
    "mode": "incrementing",
    "incrementing.column.name": "id",
    "topic.prefix": "postgres-",
    "transforms": "createKey",
    "transforms.createKey.type": "org.apache.kafka.connect.transforms.ValueToKey",
    "transforms.createKey.fields": "id"
  }
}
```

### Naming Conventions

- Files: `<source>-<type>-connector.json`
- Connectors: `<source>-<type>-<direction>`
- Topics: `<source>-<type>-<entity>`

Example:
```
File: postgresql-products-source.json
Connector: postgresql-products-source
Topic: postgresql-products-data
```

## 🆕 Adding New Connectors

### Source Connector Example

1. Create directory structure:
   ```bash
   mkdir -p source/new-type/config
   ```

2. Create configuration file:
   ```bash
   touch source/new-type/config/new-source.json
   ```

3. Configure connector:
   ```json
   {
     "name": "new-source-connector",
     "config": {
       "connector.class": "com.example.NewSourceConnector",
       "tasks.max": "1",
       // Add connector-specific configuration
     }
   }
   ```

### Sink Connector Example

1. Create directory structure:
   ```bash
   mkdir -p sink/new-type/config
   ```

2. Create configuration file:
   ```bash
   touch sink/new-type/config/new-sink.json
   ```

3. Configure connector:
   ```json
   {
     "name": "new-sink-connector",
     "config": {
       "connector.class": "com.example.NewSinkConnector",
       "tasks.max": "1",
       // Add connector-specific configuration
     }
   }
   ```

## 📚 Best Practices

### Security

1. Never store credentials in configuration files
   - Use environment variables: `${env:VARIABLE_NAME}`
   - Use external secrets management
   - Implement proper access controls

2. Secure sensitive data
   - Encrypt sensitive configuration values
   - Use SSL/TLS for connections
   - Implement authentication

### Performance

1. Configure optimal task count
   ```json
   {
     "config": {
       "tasks.max": "3"  // Adjust based on data volume
     }
   }
   ```

2. Use appropriate batch sizes
   ```json
   {
     "config": {
       "batch.size": "1000",
       "poll.interval.ms": "1000"
     }
   }
   ```

### Monitoring

1. Enable JMX metrics
   ```json
   {
     "config": {
       "jmx": "true",
       "jmx.prefix": "kafka.connect"
     }
   }
   ```

2. Configure error handling
   ```json
   {
     "config": {
       "errors.tolerance": "all",
       "errors.deadletterqueue.topic.name": "dlq-topic",
       "errors.deadletterqueue.topic.replication.factor": "3"
     }
   }
   ```

## 🔍 Troubleshooting

### Common Issues

1. Connector Won't Start
   - Verify configuration syntax
   - Check connector class availability
   - Validate connection parameters

2. Data Flow Issues
   - Check source/sink connectivity
   - Verify topic existence and permissions
   - Review transformation configurations

### Debugging Steps

1. Check connector status:
   ```bash
   make check-connector-status CONNECTOR=problem-connector
   ```

2. View connector logs:
   ```bash
   make connector-logs CONNECTOR=problem-connector
   ```

3. Validate configuration:
   ```bash
   make validate-config CONNECTOR=problem-connector
   ```

## 📖 Additional Resources

- [Kafka Connect Documentation](https://docs.confluent.io/platform/current/connect/index.html)
- [Connector Development Guide](https://docs.confluent.io/platform/current/connect/devguide.html)
- [Connector Configuration Examples](https://docs.confluent.io/platform/current/connect/references/allconfigs.html)

