# Sink Connectors

This directory contains configurations for all sink connectors, organized by connector type.

## Directory Structure

```
sink/
├── jdbc/            # JDBC sink connectors
│   ├── postgresql/  # PostgreSQL sink connectors
│   └── mysql/       # MySQL sink connectors
└── other_sinks/     # Other types of sink connectors
```

## JDBC Sink Connectors

### PostgreSQL

The PostgreSQL JDBC sink connector exports data from Kafka topics to PostgreSQL databases.

Key files:
- `jdbc-sink-connector.json`: Configuration for the PostgreSQL sink connector

Configuration example:
```json
{
  "name": "jdbc-sink-connector",
  "config": {
    "connector.class": "io.confluent.connect.jdbc.JdbcSinkConnector",
    "connection.url": "****************************************",
    "connection.user": "user",
    "connection.password": "password",
    "insert.mode": "upsert",
    "table.name.format": "table_name",
    "topics": "topic_name",
    "pk.fields": "id",
    "pk.mode": "record_value",
    "auto.create": "true"
  }
}
```

### MySQL

Add your MySQL sink connectors in this directory.

## Adding a New Sink Connector

1. Create a directory under the appropriate connector type
2. Add your connector configuration file
3. Document the connector in this README
4. Update the Makefile to include operations for your connector

## Sink Connector Operations

Use the Makefile to manage sink connectors:

```bash
# Create all sink connectors
make create-sink-connectors

# Create a specific sink connector
make create-sink-connector CONNECTOR=jdbc-postgresql

# Delete a specific sink connector
make delete-sink-connector CONNECTOR=jdbc-postgresql

# Check status of sink connectors
make check-sink-connectors
```

