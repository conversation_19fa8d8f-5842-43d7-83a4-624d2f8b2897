#!/bin/bash

# CDC Management Script for fahasa.m_product -> pim.m_product
# This script provides various management operations for the CDC setup

set -e

# Configuration
CONNECT_URL="http://localhost:8083"
SOURCE_CONNECTOR_NAME="fahasa-m-product-source-connector"
SINK_CONNECTOR_NAME="pim-m-product-sink-connector"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[HEADER]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  status      - Show status of all connectors"
    echo "  start       - Start/restart connectors"
    echo "  stop        - Stop connectors"
    echo "  delete      - Delete connectors"
    echo "  logs        - Show connector logs"
    echo "  topics      - List Kafka topics"
    echo "  monitor     - Monitor CDC pipeline"
    echo "  test        - Test CDC with sample data"
    echo "  help        - Show this help message"
}

# Function to check connector status
check_status() {
    print_header "=== CDC Pipeline Status ==="
    
    for connector in "$SOURCE_CONNECTOR_NAME" "$SINK_CONNECTOR_NAME"; do
        echo ""
        print_status "Checking connector: $connector"
        
        if curl -s -f "$CONNECT_URL/connectors/$connector" > /dev/null 2>&1; then
            status=$(curl -s "$CONNECT_URL/connectors/$connector/status")
            state=$(echo "$status" | jq -r '.connector.state')
            
            if [[ "$state" == "RUNNING" ]]; then
                echo -e "  State: ${GREEN}$state${NC}"
                
                # Check tasks
                tasks_running=$(echo "$status" | jq -r '.tasks[] | select(.state=="RUNNING") | .id' | wc -l)
                tasks_total=$(echo "$status" | jq -r '.tasks[] | .id' | wc -l)
                echo "  Tasks: $tasks_running/$tasks_total running"
                
                # Show any failed tasks
                failed_tasks=$(echo "$status" | jq -r '.tasks[] | select(.state!="RUNNING") | "\(.id): \(.state)"')
                if [[ -n "$failed_tasks" ]]; then
                    echo -e "  ${RED}Failed tasks:${NC}"
                    echo "$failed_tasks" | sed 's/^/    /'
                fi
            else
                echo -e "  State: ${RED}$state${NC}"
                # Show error trace if available
                error=$(echo "$status" | jq -r '.connector.trace // empty')
                if [[ -n "$error" ]]; then
                    echo -e "  ${RED}Error:${NC} $error"
                fi
            fi
        else
            echo -e "  State: ${RED}NOT FOUND${NC}"
        fi
    done
}

# Function to start connectors
start_connectors() {
    print_header "=== Starting CDC Connectors ==="
    
    for connector in "$SOURCE_CONNECTOR_NAME" "$SINK_CONNECTOR_NAME"; do
        print_status "Starting connector: $connector"
        
        response=$(curl -s -X PUT "$CONNECT_URL/connectors/$connector/resume")
        if [[ $? -eq 0 ]]; then
            print_status "Successfully started connector: $connector"
        else
            print_error "Failed to start connector: $connector"
        fi
    done
}

# Function to stop connectors
stop_connectors() {
    print_header "=== Stopping CDC Connectors ==="
    
    for connector in "$SOURCE_CONNECTOR_NAME" "$SINK_CONNECTOR_NAME"; do
        print_status "Stopping connector: $connector"
        
        response=$(curl -s -X PUT "$CONNECT_URL/connectors/$connector/pause")
        if [[ $? -eq 0 ]]; then
            print_status "Successfully stopped connector: $connector"
        else
            print_error "Failed to stop connector: $connector"
        fi
    done
}

# Function to delete connectors
delete_connectors() {
    print_header "=== Deleting CDC Connectors ==="
    
    read -p "Are you sure you want to delete all CDC connectors? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_warning "Operation cancelled"
        return
    fi
    
    for connector in "$SOURCE_CONNECTOR_NAME" "$SINK_CONNECTOR_NAME"; do
        print_status "Deleting connector: $connector"
        
        response=$(curl -s -X DELETE "$CONNECT_URL/connectors/$connector")
        if [[ $? -eq 0 ]]; then
            print_status "Successfully deleted connector: $connector"
        else
            print_error "Failed to delete connector: $connector"
        fi
    done
}

# Function to show connector logs
show_logs() {
    print_header "=== Connector Logs ==="
    
    print_status "Showing Kafka Connect logs..."
    docker logs --tail=50 connect
}

# Function to list Kafka topics
list_topics() {
    print_header "=== Kafka Topics ==="
    
    print_status "Listing all topics..."
    docker exec broker kafka-topics --bootstrap-server localhost:9092 --list | grep -E "(debezium|fahasa|pim)" || echo "No CDC-related topics found"
    
    echo ""
    print_status "Topic details for debezium.fahasa.m_product:"
    docker exec broker kafka-topics --bootstrap-server localhost:9092 --describe --topic debezium.fahasa.m_product 2>/dev/null || echo "Topic not found"
}

# Function to monitor CDC pipeline
monitor_pipeline() {
    print_header "=== CDC Pipeline Monitor ==="
    
    print_status "Monitoring CDC pipeline... (Press Ctrl+C to stop)"
    
    while true; do
        clear
        echo "$(date): CDC Pipeline Status"
        echo "================================"
        
        # Check connector status
        check_status
        
        echo ""
        print_status "Recent messages in topic:"
        docker exec broker kafka-console-consumer \
            --bootstrap-server localhost:9092 \
            --topic debezium.fahasa.m_product \
            --from-beginning \
            --max-messages 5 \
            --timeout-ms 5000 2>/dev/null || echo "No messages found"
        
        sleep 10
    done
}

# Function to test CDC with sample data
test_cdc() {
    print_header "=== Testing CDC Pipeline ==="
    
    print_status "Inserting test data into fahasa.m_product..."
    
    # Insert test data
    docker exec -i postgres psql -U postgres -d postgres << EOF
-- Create schema and table if they don't exist
CREATE SCHEMA IF NOT EXISTS fahasa;
CREATE SCHEMA IF NOT EXISTS pim;

-- Create source table if it doesn't exist
CREATE TABLE IF NOT EXISTS fahasa.m_product (
    m_product_id BIGINT PRIMARY KEY,
    client_id BIGINT NOT NULL,
    value VARCHAR(255),
    name VARCHAR(255),
    created TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create target table if it doesn't exist
CREATE TABLE IF NOT EXISTS pim.m_product (
    m_product_id BIGINT,
    client_id BIGINT,
    value VARCHAR(255),
    name VARCHAR(255),
    created TIMESTAMP,
    updated TIMESTAMP,
    PRIMARY KEY (m_product_id, client_id)
);

-- Insert test data
INSERT INTO fahasa.m_product (m_product_id, client_id, value, name) 
VALUES 
    (1001, 1, 'TEST001', 'Test Product 1'),
    (1002, 1, 'TEST002', 'Test Product 2')
ON CONFLICT (m_product_id) DO UPDATE SET
    value = EXCLUDED.value,
    name = EXCLUDED.name,
    updated = CURRENT_TIMESTAMP;

-- Show inserted data
SELECT 'Source data:' as info;
SELECT * FROM fahasa.m_product WHERE m_product_id IN (1001, 1002);
EOF

    print_status "Test data inserted. Waiting for CDC to process..."
    sleep 10
    
    print_status "Checking target table..."
    docker exec -i postgres psql -U postgres -d postgres << EOF
SELECT 'Target data:' as info;
SELECT * FROM pim.m_product WHERE m_product_id IN (1001, 1002);
EOF
}

# Main function
main() {
    case "${1:-help}" in
        status)
            check_status
            ;;
        start)
            start_connectors
            ;;
        stop)
            stop_connectors
            ;;
        delete)
            delete_connectors
            ;;
        logs)
            show_logs
            ;;
        topics)
            list_topics
            ;;
        monitor)
            monitor_pipeline
            ;;
        test)
            test_cdc
            ;;
        help|*)
            show_usage
            ;;
    esac
}

# Run main function
main "$@"
