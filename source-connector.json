{"name": "fahasa-m-product-source-connector", "config": {"connector.class": "io.debezium.connector.postgresql.PostgresConnector", "tasks.max": "1", "database.hostname": "postgres", "database.port": "5432", "database.user": "postgres", "database.password": "postgres", "database.dbname": "postgres", "database.server.name": "debezium", "table.include.list": "fahasa.m_product", "topic.prefix": "debezium", "slot.name": "fahasa_m_product_slot", "publication.name": "fahasa_m_product_publication", "plugin.name": "pgoutput", "key.converter": "org.apache.kafka.connect.json.JsonConverter", "key.converter.schemas.enable": "true", "value.converter": "org.apache.kafka.connect.json.JsonConverter", "value.converter.schemas.enable": "true", "snapshot.mode": "never", "decimal.handling.mode": "double", "time.precision.mode": "adaptive", "heartbeat.interval.ms": "10000", "heartbeat.topics.prefix": "__debezium-heartbeat", "errors.tolerance": "all", "errors.log.enable": "true", "errors.log.include.messages": "true", "max.batch.size": "2048", "max.queue.size": "8192", "poll.interval.ms": "1000"}}