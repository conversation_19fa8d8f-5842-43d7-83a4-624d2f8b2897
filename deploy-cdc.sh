#!/bin/bash

# CDC Deployment Script for fahasa.m_product -> pim.m_product
# This script deploys the source and sink connectors for CDC

set -e

# Configuration
CONNECT_URL="http://localhost:8083"
SOURCE_CONNECTOR_FILE="source-connector.json"
SINK_CONNECTOR_FILE="sink-connector.json"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Kafka Connect is ready
check_connect_ready() {
    print_status "Checking if Kafka Connect is ready..."
    
    for i in {1..30}; do
        if curl -s -f "$CONNECT_URL" > /dev/null 2>&1; then
            print_status "Kafka Connect is ready!"
            return 0
        fi
        print_warning "Waiting for Kafka Connect to be ready... (attempt $i/30)"
        sleep 5
    done
    
    print_error "Kafka Connect is not ready after 150 seconds"
    return 1
}

# Function to deploy a connector
deploy_connector() {
    local config_file=$1
    local connector_name=$(jq -r '.name' "$config_file")
    
    print_status "Deploying connector: $connector_name"
    
    # Check if connector already exists
    if curl -s -f "$CONNECT_URL/connectors/$connector_name" > /dev/null 2>&1; then
        print_warning "Connector $connector_name already exists. Updating..."
        
        # Update existing connector
        response=$(curl -s -X PUT \
            -H "Content-Type: application/json" \
            -d @"$config_file" \
            "$CONNECT_URL/connectors/$connector_name/config")
        
        if [[ $? -eq 0 ]]; then
            print_status "Successfully updated connector: $connector_name"
        else
            print_error "Failed to update connector: $connector_name"
            echo "Response: $response"
            return 1
        fi
    else
        # Create new connector
        response=$(curl -s -X POST \
            -H "Content-Type: application/json" \
            -d @"$config_file" \
            "$CONNECT_URL/connectors")
        
        if [[ $? -eq 0 ]]; then
            print_status "Successfully created connector: $connector_name"
        else
            print_error "Failed to create connector: $connector_name"
            echo "Response: $response"
            return 1
        fi
    fi
}

# Function to check connector status
check_connector_status() {
    local connector_name=$1
    
    print_status "Checking status of connector: $connector_name"
    
    status=$(curl -s "$CONNECT_URL/connectors/$connector_name/status")
    state=$(echo "$status" | jq -r '.connector.state')
    
    if [[ "$state" == "RUNNING" ]]; then
        print_status "Connector $connector_name is RUNNING"
        
        # Check task status
        tasks=$(echo "$status" | jq -r '.tasks[].state' | sort | uniq -c)
        echo "Task states: $tasks"
    else
        print_error "Connector $connector_name is in state: $state"
        echo "Full status: $status"
        return 1
    fi
}

# Function to setup PostgreSQL for CDC
setup_postgres_cdc() {
    print_status "Setting up PostgreSQL for CDC..."
    
    # Create replication slot and publication
    docker exec -i postgres psql -U postgres -d postgres << EOF
-- Create publication for fahasa.m_product table
DROP PUBLICATION IF EXISTS fahasa_m_product_publication;
CREATE PUBLICATION fahasa_m_product_publication FOR TABLE fahasa.m_product;

-- Grant necessary permissions
GRANT SELECT ON fahasa.m_product TO postgres;
GRANT USAGE ON SCHEMA fahasa TO postgres;

-- Show current publications
SELECT * FROM pg_publication;
EOF

    if [[ $? -eq 0 ]]; then
        print_status "PostgreSQL CDC setup completed successfully"
    else
        print_error "Failed to setup PostgreSQL for CDC"
        return 1
    fi
}

# Main deployment function
main() {
    print_status "Starting CDC deployment for fahasa.m_product -> pim.m_product"
    
    # Check if config files exist
    if [[ ! -f "$SOURCE_CONNECTOR_FILE" ]]; then
        print_error "Source connector file not found: $SOURCE_CONNECTOR_FILE"
        exit 1
    fi
    
    if [[ ! -f "$SINK_CONNECTOR_FILE" ]]; then
        print_error "Sink connector file not found: $SINK_CONNECTOR_FILE"
        exit 1
    fi
    
    # Check if Kafka Connect is ready
    if ! check_connect_ready; then
        exit 1
    fi
    
    # Setup PostgreSQL for CDC
    if ! setup_postgres_cdc; then
        exit 1
    fi
    
    # Deploy source connector
    if ! deploy_connector "$SOURCE_CONNECTOR_FILE"; then
        exit 1
    fi
    
    # Wait a bit for source connector to initialize
    sleep 10
    
    # Deploy sink connector
    if ! deploy_connector "$SINK_CONNECTOR_FILE"; then
        exit 1
    fi
    
    # Wait a bit for connectors to start
    sleep 15
    
    # Check connector statuses
    source_connector_name=$(jq -r '.name' "$SOURCE_CONNECTOR_FILE")
    sink_connector_name=$(jq -r '.name' "$SINK_CONNECTOR_FILE")
    
    check_connector_status "$source_connector_name"
    check_connector_status "$sink_connector_name"
    
    print_status "CDC deployment completed successfully!"
    print_status "Source: fahasa.m_product -> Topic: debezium.fahasa.m_product -> Sink: pim.m_product"
}

# Run main function
main "$@"
