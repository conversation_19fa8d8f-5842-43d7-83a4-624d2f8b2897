# CDC Configuration: fahasa.m_product → pim.m_product

This document describes the Change Data Capture (CDC) setup for replicating data from `fahasa.m_product` to `pim.m_product` using Debezium and Kafka Connect.

## 📋 Overview

The CDC pipeline captures changes from the source table `fahasa.m_product` and replicates them to the target table `pim.m_product` in real-time using:

- **Source**: Debezium PostgreSQL Connector
- **Message Broker**: Apache Kafka
- **Sink**: Confluent JDBC Sink Connector
- **Target**: PostgreSQL table `pim.m_product`

## 🏗️ Architecture

```
fahasa.m_product (Source)
        ↓
Debezium PostgreSQL Connector
        ↓
Kafka Topic: debezium.fahasa.m_product
        ↓
JDBC Sink Connector
        ↓
pim.m_product (Target)
```

## 📁 Files

- `source-connector.json` - Debezium source connector configuration
- `sink-connector.json` - JDBC sink connector configuration
- `deploy-cdc.sh` - Deployment script for CDC setup
- `manage-cdc.sh` - Management and monitoring script
- `CDC-README.md` - This documentation

## 🚀 Quick Start

### Prerequisites

1. Ensure Docker and Docker Compose are installed
2. Start the Confluent Platform and PostgreSQL:
   ```bash
   docker-compose -f docker/compose/docker-compose-postgres.yml up -d
   docker-compose -f docker/compose/docker-compose-confluent-platform.yml up -d
   ```

3. Wait for all services to be ready (especially Kafka Connect on port 8083)

### Deploy CDC Pipeline

1. **Deploy the connectors**:
   ```bash
   ./deploy-cdc.sh
   ```

2. **Check status**:
   ```bash
   ./manage-cdc.sh status
   ```

3. **Test the pipeline**:
   ```bash
   ./manage-cdc.sh test
   ```

## 🔧 Configuration Details

### Source Connector (Debezium)

<augment_code_snippet path="source-connector.json" mode="EXCERPT">
````json
{
  "name": "fahasa-m-product-source-connector",
  "config": {
    "connector.class": "io.debezium.connector.postgresql.PostgresConnector",
    "table.include.list": "fahasa.m_product",
    "topic.prefix": "debezium"
  }
}
````
</augment_code_snippet>

Key configurations:
- **Table Filter**: Only captures changes from `fahasa.m_product`
- **Topic Naming**: Creates topic `debezium.fahasa.m_product`
- **Snapshot Mode**: Initial snapshot + ongoing changes
- **Replication Slot**: `fahasa_m_product_slot`

### Sink Connector (JDBC)

<augment_code_snippet path="sink-connector.json" mode="EXCERPT">
````json
{
  "name": "pim-m-product-sink-connector",
  "config": {
    "connector.class": "io.confluent.connect.jdbc.JdbcSinkConnector",
    "topics": "debezium.fahasa.m_product",
    "table.name.format": "pim.m_product"
  }
}
````
</augment_code_snippet>

Key configurations:
- **Insert Mode**: Upsert (insert or update)
- **Primary Keys**: `m_product_id`, `client_id`
- **Auto Create**: Automatically creates target table
- **Delete Handling**: Enabled for DELETE operations

## 🛠️ Management Commands

### Status and Monitoring

```bash
# Check connector status
./manage-cdc.sh status

# Monitor pipeline in real-time
./manage-cdc.sh monitor

# View connector logs
./manage-cdc.sh logs

# List Kafka topics
./manage-cdc.sh topics
```

### Control Operations

```bash
# Start/resume connectors
./manage-cdc.sh start

# Stop/pause connectors
./manage-cdc.sh stop

# Delete connectors
./manage-cdc.sh delete
```

### Testing

```bash
# Test CDC with sample data
./manage-cdc.sh test
```

## 📊 Monitoring

### Kafka Connect REST API

- **Connect Cluster**: http://localhost:8083
- **Connector Status**: http://localhost:8083/connectors/{connector-name}/status
- **Connector Config**: http://localhost:8083/connectors/{connector-name}/config

### Confluent Control Center

- **URL**: http://localhost:9021
- **Features**: Visual monitoring, topic management, connector management

### Key Metrics to Monitor

1. **Connector State**: Should be "RUNNING"
2. **Task State**: All tasks should be "RUNNING"
3. **Lag**: Monitor replication lag
4. **Error Rate**: Check for failed messages
5. **Throughput**: Messages per second

## 🔍 Troubleshooting

### Common Issues

1. **Connector Won't Start**
   ```bash
   # Check connector status
   ./manage-cdc.sh status
   
   # Check logs
   ./manage-cdc.sh logs
   ```

2. **No Data Flowing**
   ```bash
   # Check if source table has changes
   # Check Kafka topic for messages
   ./manage-cdc.sh topics
   ```

3. **Permission Issues**
   ```bash
   # Ensure PostgreSQL user has proper permissions
   docker exec -i postgres psql -U postgres -d postgres -c "
   GRANT SELECT ON fahasa.m_product TO postgres;
   GRANT USAGE ON SCHEMA fahasa TO postgres;
   "
   ```

### Log Locations

- **Kafka Connect**: `docker logs connect`
- **PostgreSQL**: `docker logs postgres`
- **Kafka Broker**: `docker logs broker`

## 🔐 Security Considerations

1. **Database Credentials**: Use environment variables or secrets management
2. **Network Security**: Ensure proper network isolation
3. **SSL/TLS**: Enable encryption for production environments
4. **Access Control**: Implement proper RBAC for Kafka and databases

## 📈 Performance Tuning

### Source Connector

- `max.batch.size`: Adjust based on data volume
- `poll.interval.ms`: Tune polling frequency
- `max.queue.size`: Buffer size for events

### Sink Connector

- `batch.size`: Number of records per batch
- `tasks.max`: Parallel processing tasks
- `connection.pool.size`: Database connection pool

## 🔄 Schema Evolution

The CDC pipeline supports schema evolution:

- **Auto Evolution**: Enabled in sink connector
- **Schema Registry**: Manages schema versions
- **Backward Compatibility**: Maintains compatibility with existing data

## 📝 Data Flow Example

1. **Insert/Update** in `fahasa.m_product`:
   ```sql
   INSERT INTO fahasa.m_product (m_product_id, client_id, value, name)
   VALUES (1001, 1, 'PROD001', 'Sample Product');
   ```

2. **Debezium captures** the change and publishes to Kafka topic

3. **JDBC Sink** consumes the message and applies to `pim.m_product`:
   ```sql
   INSERT INTO pim.m_product (m_product_id, client_id, value, name)
   VALUES (1001, 1, 'PROD001', 'Sample Product')
   ON CONFLICT (m_product_id, client_id) DO UPDATE SET ...;
   ```

## 🆘 Support

For issues and questions:

1. Check connector logs: `./manage-cdc.sh logs`
2. Verify connector status: `./manage-cdc.sh status`
3. Test with sample data: `./manage-cdc.sh test`
4. Review Kafka topics: `./manage-cdc.sh topics`
