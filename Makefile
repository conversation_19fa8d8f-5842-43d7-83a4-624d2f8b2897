# Makefile for Confluent Platform

.PHONY: help up down logs ps clean

# Variables
COMPOSE_CONFLUENT := docker-compose -f docker/compose/docker-compose-confluent-platform.yml
COMPOSE_POSTGRES := docker-compose -f docker/compose/docker-compose-postgres.yml

help:
	@echo "Usage: make [target]"
	@echo ""
	@echo "Targets:"
	@echo "  up-confluent    Start Confluent Platform services"
	@echo "  down-confluent  Stop Confluent Platform services"
	@echo "  logs-confluent  View logs for Confluent Platform services"
	@echo "  ps-confluent    List Confluent Platform services"
	@echo ""
	@echo "  up-postgres     Start Postgres service"
	@echo "  down-postgres   Stop Postgres service"
	@echo "  logs-postgres   View logs for Postgres service"
	@echo "  ps-postgres     List Postgres service"
	@echo ""
	@echo "  up              Start all services (Confluent Platform and Postgres)"
	@echo "  down            Stop all services"
	@echo "  logs            View logs for all services"
	@echo "  ps              List all services"
	@echo ""
	@echo "  clean           Remove all stopped containers and networks"
	@echo ""
	@echo "  # Add more targets here for connector management, etc."

# Confluent Platform Targets
up-confluent:
	$(COMPOSE_CONFLUENT) up -d

down-confluent:
	$(COMPOSE_CONFLUENT) down

logs-confluent:
	$(COMPOSE_CONFLUENT) logs -f

ps-confluent:
	$(COMPOSE_CONFLUENT) ps

# Postgres Targets
up-postgres:
	$(COMPOSE_POSTGRES) up -d

down-postgres:
	$(COMPOSE_POSTGRES) down

logs-postgres:
	$(COMPOSE_POSTGRES) logs -f

ps-postgres:
	$(COMPOSE_POSTGRES) ps

# Combined Targets
up: up-confluent up-postgres

down: down-confluent down-postgres

logs:
	@echo "--- Confluent Platform Logs ---"
	@$(COMPOSE_CONFLUENT) logs -f & \
	echo "--- Postgres Logs ---"; \
	$(COMPOSE_POSTGRES) logs -f

ps:
	@echo "--- Confluent Platform Services ---"
	@$(COMPOSE_CONFLUENT) ps
	@echo ""
	@echo "--- Postgres Services ---"
	@$(COMPOSE_POSTGRES) ps

# Clean up
clean: down
	docker-compose -f docker/compose/docker-compose-confluent-platform.yml -f docker/compose/docker-compose-postgres.yml rm -sfv
	docker network prune -f

# Future Connector Management (Placeholder)
# list-connectors:
# 	@echo "Listing connectors..."
# 	# Add your script or command here, e.g., ./scripts/connector-management/list_connectors.sh

# create-connector:
# 	@echo "Creating connector..."
# 	# Add your script or command here, e.g., ./scripts/connector-management/create_connector.sh --config <config_file>

# delete-connector:
# 	@echo "Deleting connector..."
# 	# Add your script or command here, e.g., ./scripts/connector-management/delete_connector.sh --name <connector_name>