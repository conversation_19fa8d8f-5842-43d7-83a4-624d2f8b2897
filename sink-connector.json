{"name": "pim-m-product-sink-connector", "config": {"connector.class": "io.confluent.connect.jdbc.JdbcSinkConnector", "tasks.max": "1", "connection.url": "****************************************", "connection.user": "postgres", "connection.password": "postgres", "topics": "debezium.fahasa.m_product", "table.name.format": "pim.m_product", "insert.mode": "upsert", "delete.enabled": "true", "auto.create": "true", "auto.evolve": "true", "batch.size": "3000", "pk.mode": "record_key", "key.converter": "org.apache.kafka.connect.json.JsonConverter", "key.converter.schemas.enable": "true", "value.converter": "org.apache.kafka.connect.json.JsonConverter", "value.converter.schemas.enable": "true", "transforms": "unwrap", "transforms.unwrap.type": "io.debezium.transforms.ExtractNewRecordState", "transforms.unwrap.drop.tombstones": "false", "errors.tolerance": "all", "errors.log.enable": "true", "errors.log.include.messages": "true", "max.retries": "3", "retry.backoff.ms": "1000"}}