#!/bin/bash

export REMOTE_HOST=**************
export REMOTE_PORT=5432
export REMOTE_DB=idempiere
export REMOTE_USER=thai.hv
export REMOTE_PASSWORD="mkW1XiNN5Jo#b3pEF6CkjF1IA9kU472z"
export REMOTE_SCHEMA=adempiere

export LOCAL_HOST=************
export LOCAL_PORT=5432
export LOCAL_DB=postgres
export LOCAL_USER=postgres
export LOCAL_PASSWORD=postgres
export LOCAL_SCHEMA=fahasa

#!/bin/bash

# Function to transfer table data
transfer_table() {
    local query="$1"
    local table_name="$2"
    local create_table_query="$3"
    local csv_file="${table_name}.csv"
    local schema_qualified_table="$LOCAL_SCHEMA.$table_name"
    
    echo "Processing table: $schema_qualified_table"
    
    # Drop table in local DB first (before exporting)
    echo "Dropping table $schema_qualified_table if exists..."
    PGPASSWORD=$LOCAL_PASSWORD psql -h $LOCAL_HOST -p $LOCAL_PORT -U $LOCAL_USER -d $LOCAL_DB -c "DROP TABLE IF EXISTS $schema_qualified_table"
    if [ $? -ne 0 ]; then 
        echo "Lỗi xóa bảng $schema_qualified_table"
        return 1
    fi
    
    # Export data from remote to CSV
    echo "Exporting data from remote..."
    PGPASSWORD=$REMOTE_PASSWORD psql -h $REMOTE_HOST -p $REMOTE_PORT -U $REMOTE_USER -d $REMOTE_DB -c "\copy ($query) TO '$csv_file' CSV HEADER"
    if [ $? -ne 0 ]; then 
        echo "Lỗi xuất CSV cho bảng $schema_qualified_table"
        return 1
    fi
    
    # Create table in local DB
    echo "Creating table structure..."
    PGPASSWORD=$LOCAL_PASSWORD psql -h $LOCAL_HOST -p $LOCAL_PORT -U $LOCAL_USER -d $LOCAL_DB -c "$create_table_query"
    if [ $? -ne 0 ]; then 
        echo "Lỗi tạo bảng $schema_qualified_table"
        return 1
    fi
    
    # Import data from CSV to local
    echo "Importing data to local database..."
    PGPASSWORD=$LOCAL_PASSWORD psql -h $LOCAL_HOST -p $LOCAL_PORT -U $LOCAL_USER -d $LOCAL_DB -c "\copy $schema_qualified_table FROM '$csv_file' CSV HEADER"
    if [ $? -ne 0 ]; then 
        echo "Lỗi nhập CSV cho bảng $schema_qualified_table"
        return 1
    fi
    
    # Clean up CSV file
    rm -f $csv_file
    echo "Hoàn tất transfer bảng: $schema_qualified_table"
    return 0
}

# # Input connection information
# read -p "Remote host: " REMOTE_HOST
# read -p "Remote port: " REMOTE_PORT
# read -p "Remote DB: " REMOTE_DB
# read -p "Remote user: " REMOTE_USER
# read -s -p "Remote password: " REMOTE_PASSWORD; echo
# read -p "Local host: " LOCAL_HOST
# read -p "Local port: " LOCAL_PORT
# read -p "Local DB: " LOCAL_DB
# read -p "Local user: " LOCAL_USER
# read -s -p "Local password: " LOCAL_PASSWORD; echo


M_Product_remote_query="SELECT M_Product_ID, Value, Name, M_AttributeSetInstance_ID FROM $REMOTE_SCHEMA.M_Product WHERE created > '2025-01-01 00:00:00'"
M_Product_local_queries=(
    "DROP TABLE IF EXISTS $LOCAL_SCHEMA.M_Product;"
    "CREATE TABLE IF NOT EXISTS $LOCAL_SCHEMA.M_Product (M_Product_ID BIGINT PRIMARY KEY, Value VARCHAR(255), Name VARCHAR(255), M_AttributeSetInstance_ID BIGINT);"
)

M_AttributeSetInstance_remote_query="SELECT M_AttributeSetInstance_ID FROM $REMOTE_SCHEMA.M_AttributeSetInstance"
M_AttributeSetInstance_local_queries=(
    "DROP TABLE IF EXISTS $LOCAL_SCHEMA.M_AttributeSetInstance;"
    "CREATE TABLE IF NOT EXISTS $LOCAL_SCHEMA.M_AttributeSetInstance (M_AttributeSetInstance_ID BIGINT);"
)

M_AttributeInstance_remote_query="SELECT M_AttributeSetInstance_ID, M_Attribute_ID, Value FROM $REMOTE_SCHEMA.M_AttributeInstance"
M_AttributeInstance_local_queries=(
    "DROP TABLE IF EXISTS $LOCAL_SCHEMA.M_AttributeInstance;"
    "CREATE TABLE IF NOT EXISTS $LOCAL_SCHEMA.M_AttributeInstance (M_AttributeSetInstance_ID BIGINT, M_Attribute_ID BIGINT, Value VARCHAR(255));"
)

M_Attribute_remote_query="SELECT M_Attribute_ID, Name FROM $REMOTE_SCHEMA.M_Attribute"
M_Attribute_local_queries=(
    "DROP TABLE IF EXISTS $LOCAL_SCHEMA.M_Attribute;"
    "CREATE TABLE IF NOT EXISTS $LOCAL_SCHEMA.M_Attribute (M_Attribute_ID BIGINT, Name VARCHAR(255));"
)

M_AttributeValue_remote_query="SELECT M_AttributeValue_ID, Value, Name FROM $REMOTE_SCHEMA.M_AttributeValue"
M_AttributeValue_local_queries=(
    "DROP TABLE IF EXISTS $LOCAL_SCHEMA.M_AttributeValue;"
    "CREATE TABLE IF NOT EXISTS $LOCAL_SCHEMA.M_AttributeValue (M_AttributeValue_ID BIGINT, Value VARCHAR(255), Name VARCHAR(255));"
)

M_PriceList_Version_remote_query="SELECT M_PriceList_Version_ID, Name, M_PriceList_ID, ValidFrom FROM $REMOTE_SCHEMA.M_PriceList_Version"
M_PriceList_Version_local_queries=(
    "DROP TABLE IF EXISTS $LOCAL_SCHEMA.M_PriceList_Version;"
    "CREATE TABLE IF NOT EXISTS $LOCAL_SCHEMA.M_PriceList_Version (M_PriceList_Version_ID BIGINT, Name VARCHAR(255), M_PriceList_ID BIGINT, ValidFrom TIMESTAMP);"
)

M_PriceList_remote_query="SELECT M_PriceList_ID, Name FROM $REMOTE_SCHEMA.M_PriceList"
M_PriceList_local_queries=(
    "DROP TABLE IF EXISTS $LOCAL_SCHEMA.M_PriceList;"
    "CREATE TABLE IF NOT EXISTS $LOCAL_SCHEMA.M_PriceList (M_PriceList_ID BIGINT, Name VARCHAR(255));"
)

M_ProductPrice_remote_query="WITH last_sales_pl AS (SELECT v.m_pricelist_version_id FROM m_pricelist pl JOIN m_pricelist_version v on pl.m_pricelist_id = v.m_pricelist_id WHERE pl.name = 'Sales PL' order by v.validfrom desc LIMIT 1), last_internal_last_sales_pl AS (SELECT v.m_pricelist_version_id FROM m_pricelist pl JOIN m_pricelist_version v on pl.m_pricelist_id = v.m_pricelist_id WHERE pl.name = 'Internal Sales PL' order by v.validfrom desc LIMIT 1), last_purchase_pl AS (SELECT v.m_pricelist_version_id FROM m_pricelist pl JOIN m_pricelist_version v on pl.m_pricelist_id = v.m_pricelist_id WHERE pl.name = 'Purchase PL' order by v.validfrom desc LIMIT 1) SELECT m_productprice_id, m_product_id, pp.m_pricelist_version_id, pricelist FROM $REMOTE_SCHEMA.m_productprice pp INNER JOIN last_sales_pl t1 ON pp.m_pricelist_version_id = t1.m_pricelist_version_id UNION ALL SELECT m_productprice_id, m_product_id, pp.m_pricelist_version_id, pricelist FROM $REMOTE_SCHEMA.m_productprice pp INNER JOIN last_internal_last_sales_pl t1 ON pp.m_pricelist_version_id = t1.m_pricelist_version_id UNION ALL SELECT m_productprice_id, m_product_id, pp.m_pricelist_version_id, pricelist FROM $REMOTE_SCHEMA.m_productprice pp INNER JOIN last_purchase_pl t1 ON pp.m_pricelist_version_id = t1.m_pricelist_version_id"

M_ProductPrice_local_queries=(
    "DROP TABLE IF EXISTS $LOCAL_SCHEMA.M_ProductPrice;"
    "CREATE TABLE IF NOT EXISTS $LOCAL_SCHEMA.M_ProductPrice (M_ProductPrice_ID BIGINT, M_Product_ID BIGINT, M_PriceList_Version_ID BIGINT, PriceList DECIMAL(19,6));"
)

# Define table configurations
declare -A table_configs=(
    ["m_product"]="$M_Product_remote_query|${M_Product_local_queries[*]}"
    # ["m_attributesetinstance"]="$M_AttributeSetInstance_remote_query|${M_AttributeSetInstance_local_queries[*]}"
    # ["m_attributeinstance"]="$M_AttributeInstance_remote_query|${M_AttributeInstance_local_queries[*]}"
    # ["m_attribute"]="$M_Attribute_remote_query|${M_Attribute_local_queries[*]}"
    # ["m_attributevalue"]="$M_AttributeValue_remote_query|${M_AttributeValue_local_queries[*]}"
    # ["m_pricelist_version"]="$M_PriceList_Version_remote_query|${M_PriceList_Version_local_queries[*]}"
    # ["m_pricelist"]="$M_PriceList_remote_query|${M_PriceList_local_queries[*]}"
    # ["m_productprice"]="$M_ProductPrice_remote_query|${M_ProductPrice_local_queries[*]}"
)

# create local schema
PGPASSWORD=$LOCAL_PASSWORD psql -h $LOCAL_HOST -p $LOCAL_PORT -U $LOCAL_USER -d $LOCAL_DB -c "CREATE SCHEMA IF NOT EXISTS $LOCAL_SCHEMA"
if [ $? -ne 0 ]; then 
    echo "Lỗi tạo schema $LOCAL_SCHEMA"
    exit 1
fi

# Process each table
for table_name in "${!table_configs[@]}"; do
    IFS='|' read -r query create_query <<< "${table_configs[$table_name]}"
    
    if ! transfer_table "$query" "$table_name" "$create_query"; then
        echo "Lỗi xử lý bảng $table_name, dừng script"
        exit 1
    fi
done

# Show all tables in local schema
PGPASSWORD=$LOCAL_PASSWORD psql -h $LOCAL_HOST -p $LOCAL_PORT -U $LOCAL_USER -d $LOCAL_DB -c "SELECT table_name FROM information_schema.tables WHERE table_schema = '$LOCAL_SCHEMA'"

echo "Hoàn tất tất cả transfers"