# 🐳 Docker Compose Configuration

A comprehensive guide for deploying and managing Confluent Platform components using Docker Compose.

## 🎯 Purpose and Overview

This directory provides:
- Production-ready Docker Compose configurations for Confluent Platform
- Database service configurations for connector testing
- Volume management for data persistence
- Environment-specific deployment options
- Monitoring and logging setup

## 📁 Directory Structure

```
docker/
├── compose/                # Docker Compose configurations
│   ├── docker-compose.yml  # Main Confluent Platform stack
│   └── postgres.yml        # PostgreSQL services
└── volumes/               # Persistent data storage
    ├── kafka/            # Kafka broker data
    ├── zookeeper/        # ZooKeeper data
    ├── schema-registry/  # Schema Registry data
    └── postgres/         # PostgreSQL data
```

## 🚀 Quick Start

### Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- At least 8GB RAM
- 20GB free disk space

### Basic Usage

```bash
# Start all services
make start-all

# Start only Confluent Platform
make start-confluent-platform

# Start only databases
make start-postgres

# Stop all services
make stop-all

# View service logs
make logs SERVICE=connect
```

## ⚙️ Service Configuration

### Confluent Platform Stack

The main `docker-compose.yml` includes:

```yaml
services:
  zookeeper:
    image: confluentinc/cp-zookeeper:7.9.1
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      
  broker:
    image: confluentinc/cp-kafka:7.9.1
    depends_on:
      - zookeeper
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      
  schema-registry:
    image: confluentinc/cp-schema-registry:7.9.1
    depends_on:
      - broker
      
  connect:
    image: confluentinc/cp-kafka-connect:7.9.1
    volumes:
      - ../connectors:/etc/kafka-connect/jars
      
  control-center:
    image: confluentinc/cp-enterprise-control-center:7.9.1
    ports:
      - "9021:9021"
```

### PostgreSQL Services

The `postgres.yml` includes source and sink databases:

```yaml
services:
  postgres-source:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: sourcedb
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      
  postgres-sink:
    image: postgres:15-alpine
    ports:
      - "5433:5432"
    environment:
      POSTGRES_DB: sinkdb
```

## 🔧 Advanced Configuration

### Memory Settings

Adjust service memory limits in `docker-compose.yml`:

```yaml
services:
  broker:
    environment:
      KAFKA_HEAP_OPTS: "-Xmx2G -Xms2G"
    deploy:
      resources:
        limits:
          memory: 3G
```

### Network Configuration

Create custom networks for service isolation:

```yaml
networks:
  kafka_net:
    driver: bridge
  db_net:
    driver: bridge

services:
  broker:
    networks:
      - kafka_net
  postgres:
    networks:
      - db_net
```

### Volume Management

Configure persistent storage:

```yaml
volumes:
  kafka_data:
    driver: local
  postgres_data:
    driver: local

services:
  broker:
    volumes:
      - kafka_data:/var/lib/kafka/data
  postgres:
    volumes:
      - postgres_data:/var/lib/postgresql/data
```

## 📊 Monitoring & Logging

### Prometheus & Grafana Integration

```yaml
services:
  prometheus:
    image: prom/prometheus
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      
  grafana:
    image: grafana/grafana
    depends_on:
      - prometheus
    ports:
      - "3000:3000"
```

### Log Management

```yaml
services:
  connect:
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

## 🔍 Troubleshooting

### Common Issues

1. Service Won't Start
   - Check container logs: `docker-compose logs <service>`
   - Verify memory/disk space
   - Check port conflicts

2. Connection Issues
   - Verify network configuration
   - Check service dependencies
   - Validate environment variables

### Health Checks

```yaml
services:
  connect:
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8083/connectors"]
      interval: 30s
      timeout: 10s
      retries: 3
```

## 🔒 Security Configuration

### SSL/TLS Setup

```yaml
services:
  broker:
    environment:
      KAFKA_SSL_KEYSTORE_LOCATION: "/etc/kafka/secrets/kafka.broker.keystore.jks"
      KAFKA_SSL_KEYSTORE_PASSWORD: "${SSL_KEYSTORE_PASSWORD}"
      KAFKA_SSL_KEY_PASSWORD: "${SSL_KEY_PASSWORD}"
    volumes:
      - ./secrets:/etc/kafka/secrets
```

### SASL Authentication

```yaml
services:
  broker:
    environment:
      KAFKA_SASL_ENABLED_MECHANISMS: "PLAIN"
      KAFKA_SASL_MECHANISM_INTER_BROKER_PROTOCOL: "PLAIN"
```

## 📝 Environment Variables

Create `.env` files for different environments:

```bash
# .env.dev
KAFKA_BROKER_ID=1
KAFKA_ZOOKEEPER_CONNECT=zookeeper:2181
KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://broker:9092

# .env.prod
KAFKA_BROKER_ID=1
KAFKA_ZOOKEEPER_CONNECT=zookeeper:2181,zookeeper2:2181,zookeeper3:2181
KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://broker.example.com:9092
```

## 📚 Additional Resources

- [Confluent Docker Documentation](https://docs.confluent.io/platform/current/installation/docker/index.html)
- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [Kafka Configuration Parameters](https://docs.confluent.io/platform/current/installation/configuration/)

